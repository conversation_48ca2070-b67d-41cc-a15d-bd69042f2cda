import { ChangeDetectorRef, Component, ViewChild, AfterViewInit } from '@angular/core';
import { TypeSenseService } from '../../services/typesense.service';
import { Product, CartItem, ProductSearchResult } from '../../models';
import { CartCalculationUtils } from 'src/app/utils/cart-calculation.utils';
import { SharedModule } from 'src/app/shared.module';
import { AutoComplete } from 'primeng/autocomplete';

// Simple billing tab interface
interface SimpleBillingTab {
  title: string;
  value: number;
  items: CartItem[];
}

@Component({
  selector: 'app-home',
  templateUrl: 'home.page.html',
  standalone: true,
  imports: [
    SharedModule
  ],
})

export class HomePage implements AfterViewInit {
  @ViewChild('searchInput') searchInput!: AutoComplete;
  currentTab = 0;
  billingTabs: SimpleBillingTab[] = [
    { title: 'Billing', value: 0, items: [] as CartItem[] }
  ];
  searchSuggestions: Product[] = [];
  searchText: string = '';
  productsColumns: any[] = [];
  scanDetection = {
    buffer: '',
    lastKeyTime: 0,
    scanned: false
  }
  private lastCartAmount = 0;

  constructor(
    public typesenseService: TypeSenseService,
    private cdr: ChangeDetectorRef
  ) { }

  ionViewDidEnter() {
    this.searchSuggestions = [];
    this.currentTab = 0;
    this.searchText = '';
    this.scanDetection = {buffer: '', lastKeyTime: 0, scanned: false}
    this.productsColumns = [
      { field: 'thumbnail_image', header: 'Image', type: 'image', width: '55px' },
      { field: 'child_sku', header: 'SKU', width: '100px' },
      { field: 'name', header: 'Name' , body: (item: any) => item.is_freebie ? `${item.name} (FREE)` : item.name, class: (item: any) => item.is_freebie ? 'text-green-600 font-semibold' : ''},
      { field: 'variant_name', header: 'Variant', width: '70px' },
      { field: 'selling_price', header: 'Price', body: (item: any) => item.is_freebie ? 'FREE' : CartCalculationUtils.formatCurrency(item.selling_price), class: 'text-orange-600 font-semibold', width: '70px' },
      { field: 'discount', header: 'Discount', body: (item: any) => item.is_freebie ? 'FREE' : CartCalculationUtils.formatCurrency(CartCalculationUtils.calculateItemDiscount(item)), width: '70px' },
      { field: 'total_amount', header: 'Total Amt', body: (item: any) => item.is_freebie ? 'FREE' : CartCalculationUtils.formatCurrency(CartCalculationUtils.calculateItemTotal(item)), width: '80px' },
      { field: 'quantity', header: 'Quantity', body: (item: any) => item.is_freebie ? 'FREE' : item.quantity, width: '100px' },
      {
        header: 'Actions',
        field: 'actions',
        body: (item: any) => item.is_freebie ? '<i class="pi pi-gift text-green-600"></i>' : '<i class="pi pi-trash text-red-500 cursor-pointer" title="Remove"></i>',
        width: '50px'
      },
    ];
    this.updateFreebies();
  }

  ngAfterViewInit() {
    setTimeout(() => {
      this.focusSearch();
    }, 100);
  }

  focusSearch() {
    if (this.searchInput?.inputEL) {
      const inputElement = this.searchInput.inputEL.nativeElement;
      inputElement && inputElement.focus();
    }
  }

  onInputEvent(event: any) {
    const currentTime = new Date().getTime();
    const keyInterval = currentTime - this.scanDetection.lastKeyTime;
    if (keyInterval > 0 && keyInterval < 50) {
      this.scanDetection.buffer += event.target.value.slice(-1);
    } else {
      this.scanDetection.buffer = event.target.value;
    }
    this.scanDetection.lastKeyTime = currentTime;
    if (this.scanDetection.buffer.length >= 3 && keyInterval < 50) {
      this.scanDetection.scanned = true;
    }
  }

  onSearch(event: { query: string }) {
    const query = event.query;
    if (!query || query.trim() === '') {
      this.searchSuggestions = [];
      return;
    }
    this.typesenseService.searchProductsDirectly(query.trim()).then((result: ProductSearchResult) => {
      this.searchSuggestions = result.products || [];
      if (this.scanDetection.scanned) {
        this.searchSuggestions?.length && this.onSearchSelect({ value: this.searchSuggestions[0] });
        this.scanDetection = {buffer: '', lastKeyTime: 0, scanned: false};
      }
    }).catch((error: any) => {
      console.error('Search error:', error);
      this.searchSuggestions = [];
    });
  }

  onEnterKey(event: any) {
    if (this.searchSuggestions?.length > 0) {
      event.preventDefault();
      event.stopPropagation();
      this.onSearchSelect({ value: this.searchSuggestions[0] });
    }
  }

  onSearchSelect(event: { value: Product } | Product) {
    const selectedProduct = 'value' in event ? event.value : event;
    if (selectedProduct) {
      this.addToCart(selectedProduct);
      this.searchSuggestions = [];
      this.searchText = '';
      this.searchInput.hide();
      setTimeout(() => {
        this.focusSearch();
      }, 100);
    }
  }

  async onCartChange(_cartItems: CartItem[]) {
    const currentTab = this.billingTabs[this.currentTab];
    currentTab.items = _cartItems;

    const newCartAmount = this.billingGrandTotal();
    if (Math.abs(newCartAmount - this.lastCartAmount) > 0.01) {
      this.lastCartAmount = newCartAmount;
      await this.updateFreebies();
    }

    this.refreshDisplayAndDetectChanges();
  }
 

  async onCartCleared() {
    const currentTab = this.billingTabs[this.currentTab];
    currentTab.items = [];
    this.lastCartAmount = 0;
    this.cdr.detectChanges();
  }

  // Simple freebie logic - automatically add eligible freebies to cart
  private async updateFreebies(): Promise<void> {
    const currentTab = this.billingTabs[this.currentTab];
    const cartAmount = this.billingGrandTotal();

    // Find current freebie
    const currentFreebie = currentTab.items.find(item => (item as any).is_freebie);

    if (cartAmount > 0) {
      try {
        const freebies = await this.typesenseService.getFreebiesProducts(cartAmount);
        const eligibleFreebie = freebies?.find(f => cartAmount >= f.amount);

        // Only update if freebie changed
        if (eligibleFreebie && (!currentFreebie || (currentFreebie as any).freebie_id !== eligibleFreebie.id)) {
          // Remove old freebie
          if (currentFreebie) {
            currentTab.items.splice(currentTab.items.indexOf(currentFreebie), 1);
          }
          // Add new freebie
          currentTab.items.push({
            id: `freebie_${eligibleFreebie.id}`, name: eligibleFreebie.name,
            child_sku: eligibleFreebie.child_sku || eligibleFreebie.sku, selling_price: 0, quantity: 1,
            tax: 0, cgst: 0, sgst: 0, igst: 0, cess: 0, taxable: false,
            thumbnail_image: eligibleFreebie.thumbnail_image, variant_name: eligibleFreebie.variant_name,
            is_freebie: true, freebie_id: eligibleFreebie.id, freebie_amount: eligibleFreebie.amount, freebie_name: eligibleFreebie.name
          } as any);
        } else if (!eligibleFreebie && currentFreebie) {
          // Remove freebie if no longer eligible
          currentTab.items.splice(currentTab.items.indexOf(currentFreebie), 1);
        }
      } catch (error) {
        console.error('Error updating freebies:', error);
      }
    } else if (currentFreebie) {
      // Remove freebie if cart is empty
      currentTab.items.splice(currentTab.items.indexOf(currentFreebie), 1);
    }
  }


  billingGrandTotal() {
    return this.billingTabs[this.currentTab].items
      .filter(item => !(item as any).is_freebie)
      .reduce((total, item) => total + (item.selling_price || 0) * (item.quantity || 0), 0);
  }

  async addToCart(product: Product) {
    const manualQuantity = product.quantity && product.quantity > 0 ? product.quantity : 1;
    this.updateCartItemQuantity(product, manualQuantity, true);
    product.quantity = 1;
    // Don't call refreshDisplayAndDetectChanges here as onCartChange will handle it
  }

  private updateCartItemQuantity(product: Product | string, quantity: number, isAddition: boolean = false) {
    const productSku = typeof product === 'string' ? product : product.child_sku;
    const cartItem = this.billingTabs[this.currentTab].items.find(
      (item: any) => item.child_sku === productSku,
    );
  
    if (cartItem) {
      if (isAddition) {
        cartItem.quantity += quantity;
      } else {
        cartItem.quantity = quantity;
        if (cartItem.quantity <= 0) {
          this.removeFromCart({ child_sku: productSku });
          return;
        }
      }
    } else if (isAddition && typeof product === 'object') {
      const newCartItem = { ...product, quantity: quantity, tax: product.tax };
      this.billingTabs[this.currentTab].items.push(newCartItem);
    }
  
    // Trigger cart change to update freebies
    this.onCartChange(this.billingTabs[this.currentTab].items);
  }

  private refreshDisplayAndDetectChanges() {
    this.updateDisplayedProductsFromCart();
    this.cdr.detectChanges();
  }

  removeFromCart(product: { child_sku: string }) {
    if (!this.billingTabs[this.currentTab].items) {
      return;
    }

    const index = this.billingTabs[this.currentTab].items.findIndex(
      (item: any) => item.child_sku === product.child_sku,
    );
  if (index > -1) {
      this.billingTabs[this.currentTab].items.splice(index, 1);
      // Trigger cart change to update freebies
      this.onCartChange(this.billingTabs[this.currentTab].items);
      this.refreshDisplayAndDetectChanges();
    }
  }

  updateDisplayedProductsFromCart() {
    if (this.billingTabs[this.currentTab].items && this.billingTabs[this.currentTab].items.length > 0) {
      this.billingTabs[this.currentTab].items = [...this.billingTabs[this.currentTab].items].map(item => ({
        ...item,
        quantity: item.quantity
      }));
    } else {
      this.removeTab(this.currentTab);
    }
  }
  onChange(ev: any) {
    const { event, rowData, column } = ev;
    // Don't allow quantity changes on freebies
    if (rowData.is_freebie) {
      return;
    }
    if (column.field === 'quantity') {
      rowData.quantity = event.value;
      this.updateCartItemQuantity(rowData, event.value, false);
      // Don't call refreshDisplayAndDetectChanges here as onCartChange will handle it
    }
  }
  onActionClick(ev: any) {
    const { button, rowData } = ev;
    // Don't allow actions on freebies
    if (rowData.is_freebie) {
      return;
    }
    if (button.id === 'remove') {
      this.removeFromCart(rowData);
    }
  }

  onTabChange(ev: any) {
    this.currentTab = ev;
    this.updateFreebies();
  }
  removeTab(index: number) {
    if (this.billingTabs.length === 1) {
      this.billingTabs[0].items = [];
    } else {
      this.billingTabs.splice(index, 1);
    }
    this.currentTab = index === 0 ? 0 : this.billingTabs?.length === index ? index - 1 : index;
    this.updateFreebies();
    this.cdr.detectChanges();
  }

  addTab() {
    this.billingTabs.push({ title: 'Billing', value: this.billingTabs.length, items: [] });
    this.currentTab = this.billingTabs.length - 1;
  }
}
